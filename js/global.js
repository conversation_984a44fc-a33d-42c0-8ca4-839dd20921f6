/**
 * 医院药品管理系统 - 全局脚本
 * 提供通用的工具函数和组件
 */

/**
 * 全局工具类
 */
class Utils {
    /**
     * 显示消息提示
     * @param {string} message 消息内容
     * @param {string} type 消息类型：success, error, warning, info
     * @param {number} duration 显示时长（毫秒）
     */
    static showMessage(message, type = 'info', duration = 3000) {
        // 创建消息容器
        let messageContainer = document.getElementById('messageContainer');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'messageContainer';
            messageContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(messageContainer);
        }
        
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `alert alert-${type} alert-dismissible fade show`;
        messageEl.style.cssText = `
            margin-bottom: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            animation: slideInRight 0.3s ease;
        `;
        
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        messageEl.innerHTML = `
            <i class="${iconMap[type]}"></i>
            <span style="margin-left: 8px;">${message}</span>
            <button type="button" class="btn-close" aria-label="Close"></button>
        `;
        
        // 添加关闭事件
        const closeBtn = messageEl.querySelector('.btn-close');
        closeBtn.addEventListener('click', () => {
            this.removeMessage(messageEl);
        });
        
        messageContainer.appendChild(messageEl);
        
        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                this.removeMessage(messageEl);
            }, duration);
        }
    }
    
    /**
     * 移除消息
     */
    static removeMessage(messageEl) {
        if (messageEl && messageEl.parentNode) {
            messageEl.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }
    }
    
    /**
     * 显示确认对话框
     * @param {string} message 确认消息
     * @param {function} onConfirm 确认回调
     * @param {function} onCancel 取消回调
     */
    static showConfirm(message, onConfirm, onCancel) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.style.zIndex = '9999';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" style="color: var(--primary-color);">
                            <i class="fas fa-question-circle"></i> 确认操作
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="confirmBtn">确认</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        const bsModal = new bootstrap.Modal(modal);
        const confirmBtn = modal.querySelector('#confirmBtn');
        
        confirmBtn.addEventListener('click', () => {
            bsModal.hide();
            if (onConfirm) onConfirm();
        });
        
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
            if (onCancel) onCancel();
        });
        
        bsModal.show();
    }
    
    /**
     * 格式化日期
     * @param {Date|string} date 日期
     * @param {string} format 格式
     */
    static formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }
    
    /**
     * 防抖函数
     * @param {function} func 要防抖的函数
     * @param {number} wait 等待时间
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * 节流函数
     * @param {function} func 要节流的函数
     * @param {number} limit 限制时间
     */
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    /**
     * 深拷贝对象
     * @param {any} obj 要拷贝的对象
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }
    
    /**
     * 生成UUID
     */
    static generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    
    /**
     * 验证表单
     * @param {HTMLFormElement} form 表单元素
     */
    static validateForm(form) {
        const errors = [];
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                const label = form.querySelector(`label[for="${field.id}"]`);
                const fieldName = label ? label.textContent.replace('*', '').trim() : field.name;
                errors.push(`${fieldName}不能为空`);
                field.classList.add('is-invalid');
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        // 验证邮箱格式
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !this.isValidEmail(field.value)) {
                errors.push('邮箱格式不正确');
                field.classList.add('is-invalid');
            }
        });
        
        // 验证手机号格式
        const phoneFields = form.querySelectorAll('input[data-type="phone"]');
        phoneFields.forEach(field => {
            if (field.value && !this.isValidPhone(field.value)) {
                errors.push('手机号格式不正确');
                field.classList.add('is-invalid');
            }
        });
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    /**
     * 验证邮箱格式
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * 验证手机号格式
     */
    static isValidPhone(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    }
    
    /**
     * 导出数据到Excel
     * @param {Array} data 数据数组
     * @param {string} filename 文件名
     */
    static exportToExcel(data, filename = 'export.xlsx') {
        // 这里可以集成第三方库如 SheetJS 来实现Excel导出
        // 目前提供一个简单的CSV导出实现
        this.exportToCSV(data, filename.replace('.xlsx', '.csv'));
    }
    
    /**
     * 导出数据到CSV
     * @param {Array} data 数据数组
     * @param {string} filename 文件名
     */
    static exportToCSV(data, filename = 'export.csv') {
        if (!data || data.length === 0) {
            this.showMessage('没有数据可导出', 'warning');
            return;
        }
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');
        
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        this.showMessage('导出成功', 'success');
    }
}

/**
 * 表格管理类
 */
class TableManager {
    constructor(tableId, options = {}) {
        this.table = document.getElementById(tableId);
        this.options = {
            sortable: true,
            searchable: true,
            ...options
        };
        this.data = [];
        this.filteredData = [];
        this.currentSort = { column: null, direction: 'asc' };

        this.init();
    }

    init() {
        if (this.options.sortable) {
            this.initSort();
        }
        if (this.options.searchable) {
            this.initSearch();
        }
    }

    /**
     * 设置表格数据
     */
    setData(data) {
        this.data = data;
        this.filteredData = [...data];
        this.render();
    }

    /**
     * 渲染表格
     */
    render() {
        // 表格渲染逻辑
        console.log('Rendering table with data:', this.filteredData);
    }

    /**
     * 初始化排序功能
     */
    initSort() {
        const headers = this.table.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                const column = header.dataset.sort;
                this.sort(column);
            });
        });
    }

    /**
     * 排序
     */
    sort(column) {
        if (this.currentSort.column === column) {
            this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            this.currentSort.column = column;
            this.currentSort.direction = 'asc';
        }

        this.filteredData.sort((a, b) => {
            const aVal = a[column];
            const bVal = b[column];

            if (this.currentSort.direction === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });

        this.render();
    }

    /**
     * 初始化搜索功能
     */
    initSearch() {
        const searchInput = document.querySelector('[data-table-search]');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.search(e.target.value);
            }, 300));
        }
    }

    /**
     * 搜索
     */
    search(keyword) {
        if (!keyword.trim()) {
            this.filteredData = [...this.data];
        } else {
            this.filteredData = this.data.filter(item => {
                return Object.values(item).some(value =>
                    String(value).toLowerCase().includes(keyword.toLowerCase())
                );
            });
        }
        this.render();
    }
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// 全局变量
window.Utils = Utils;
window.TableManager = TableManager;

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化所有弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // 表单验证
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const validation = Utils.validateForm(this);

            if (!validation.isValid) {
                Utils.showMessage(validation.errors.join('<br>'), 'error');
                return false;
            }

            // 表单验证通过，可以提交
            console.log('Form validation passed');
        });
    });
});

/**
 * 确保表格四周边框可见并保留滚动功能
 */
function ensureTableBordersVisible() {
    document.querySelectorAll('.table').forEach(table => {
        // 检查表格是否已经有适当的容器
        const hasProperContainer = 
            table.parentElement.classList.contains('table-container') || 
            table.parentElement.classList.contains('table-responsive');
        
        if (!hasProperContainer) {
            // 创建新容器
            const container = document.createElement('div');
            container.className = 'table-container';
            
            // 将表格包裹在容器中
            table.parentNode.insertBefore(container, table);
            container.appendChild(table);
        }
    });
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 确保表格边框可见
    ensureTableBordersVisible();
    
    // 其他初始化代码...
});